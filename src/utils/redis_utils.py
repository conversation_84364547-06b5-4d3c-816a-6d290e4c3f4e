from src.utils.custom_logger import get_logger
import redis
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, before_sleep
import json

class RedisUtils:
    def __init__(self, config, max_tries=None, retry_delay=None, ttl=None):
        self.logger = get_logger()
        self.config = config
        self.max_retries = max_tries
        self.retry_delay = retry_delay
        self.ttl = ttl
        self.redis_client = redis.Redis(host=self.config["data_store"]["redis"]["host"], port=self.config["data_store"]["redis"]["port"],
                                   username=self.config["data_store"]["redis"]["user"], password=self.config["data_store"]["redis"]["password"])

    def get_redis_client(self):
        return self.redis_client

    def _get_retry_decorator(self):
        """Create a retry decorator with instance-specific settings"""
        return retry(
            stop=stop_after_attempt(self.max_retries),
            wait=wait_fixed(self.retry_delay),
            retry=retry_if_exception_type((redis.ConnectionError, TimeoutError, redis.exceptions.RedisError))
        )

    def delete_single_redis_key(self, key):
        """Delete a single Redis key with retry logic"""
        @self._get_retry_decorator()
        def _delete_key():
            self.logger.info(f"Deleting key: {key}")
            if "*" in key:  # If the key contains '*', treat it as a pattern
                self.logger.info(f"Deleting nested key:")
                matched_keys = self.redis_client.keys(key)
                self.logger.info(f"Matched Keys are : {matched_keys}")
                if matched_keys:
                    # Attempt to delete the keys
                    for k in matched_keys:
                        r = self.redis_client.delete(k)
                        self.logger.info(f"Delete key response: {r}")
                    self.logger.info(f"Deleted keys matching pattern: {matched_keys}")
                else:
                    self.logger.info(f"No key found for pattern: {matched_keys}")
            else:
                if self.redis_client.exists(key):
                    self.redis_client.delete(key)
                    self.logger.info(f"Deleted specific key: {key}")
                else:
                    self.logger.info(f"No key found: {key}")

        try:
            return _delete_key()
        except Exception as e:
            self.logger.error(f"Failed to delete key '{key}': {e}")
            raise

    def delete_redis_keys(self, keys_to_delete):
        self.logger.info("Delete Started Now")
        for key in keys_to_delete:
            self.delete_single_redis_key(key)

    def update_single_redis_key(self, key, value):
        """Update a single Redis key with retry logic"""
        @self._get_retry_decorator()
        def _update_key():
            existing_value = self.redis_client.get(key)
            if existing_value is not None:
                self.logger.info(f"Existing value for key '{key}': {existing_value.decode('utf-8')}")
            else:
                self.logger.info(f"No existing value for key '{key}'.")

            self.redis_client.set(key, value, ex=self.ttl)
            self.logger.info(f"Key '{key}' set successfully with value {value} and TTL {self.ttl} seconds.")

        return _update_key

    def update_redis_key(self, data):
        for key, value in data.items():
            self.update_single_redis_key(key, value)
