from os import truncate

from src.utils.spark_utils import *
from src.utils.date_utils import *

class SnapPrice:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("snap_price")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts")
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(self.config["trading_competition"]["start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)

    
    def get_global_stocks(self):
        self.logger.info("reading global stock codes from kafka topic")
        global_stocks = self.io_utils.get_asset_data("global_stock_topic", "kafka").select(col("id").alias("_id"), col("linked_stock_id"))
        self.logger.info("successfully read global stock codes from kafka")
        return global_stocks

    def dedupe_price_and_write_to_s3(self, current_day_price, s3_path, key, limit_count, asset_name, **kwargs):
        delta_price_cnt = current_day_price.count()
        if (kwargs.get("s3_file_type") is not None) and (kwargs.get("s3_file_type") == "json"):
            previous_day_price = self.io_utils.read_json_data("{}/dt={}/hour={}/".format(s3_path, self.t_2, self.h_2))
        else:
            previous_day_price = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(s3_path, self.t_2, self.h_2))

        if (current_day_price.count()) < limit_count:
            current_day_price = previous_day_price
        else:
            previous_day_price = previous_day_price.withColumn("dt", lit(self.t_2))
            current_day_price = current_day_price.withColumn("dt", lit(self.t_1))
            current_day_price = current_day_price.union(previous_day_price)
            current_day_price = self.ops.de_dupe_dataframe(current_day_price, key, "dt")
            current_day_price = current_day_price.drop("dt")
        latest_price_cnt = current_day_price.count()

        if (kwargs.get("s3_file_type") is not None) and (kwargs.get("s3_file_type") == "json"):
            self.io_utils.write_json_file(current_day_price, "{}/dt={}/hour={}/".format(s3_path, self.t_1, self.h_1))
        else:
            self.io_utils.write_csv_file(current_day_price, "{}/dt={}/hour={}/".format(s3_path, self.t_1, self.h_1))
        self.logger.info(
            "delta count is: {} and snapshot count is: {} for {}".format(delta_price_cnt, latest_price_cnt, asset_name))
    
    
    def get_global_stock_options_price(self):
        price_snapshot_path = "{}/{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config["prices"]["global_stock_options"]["price_path"], self.config["prices"]["global_stock_options"]["price_snapshot_folder"], self.t_2, self.h_2)
        price_snapshot =self.io_utils.read_csv_file(price_snapshot_path, None, False)
        raw_price_path = "{}/{}/{}/dt={}/".format(self.bucket_path, self.config["prices"]["global_stock_options"]["price_path"], self.config["prices"]["global_stock_options"]["raw_price_folder"], self.t_1)
        raw_price =self.io_utils.read_json_data(raw_price_path, False, price_snapshot.schema, True)
        t_2_for_raw_price = self.t_1 - timedelta(days=1)
        raw_price_path_t_2 = "{}/{}/{}/dt={}/".format(self.bucket_path, self.config["prices"]["global_stock_options"]["price_path"], self.config["prices"]["global_stock_options"]["raw_price_folder"], t_2_for_raw_price)
        raw_price_t_2 = self.io_utils.read_json_data(raw_price_path_t_2, False, price_snapshot.schema, True)
        price = price_snapshot.union(raw_price).union(raw_price_t_2)
        price =self.ops.de_dupe_dataframe(price, ["optionsContractId", "globalStockId"], "updatedAt")
        price_snapshot_output_path = "{}/{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config["prices"]["global_stock_options"]["price_path"], self.config["prices"]["global_stock_options"]["price_snapshot_folder"], self.t_1, self.h_1)
        self.io_utils.write_csv_file(price, price_snapshot_output_path)
    
    def get_indo_stock_price(self):
        self.logger.info("fetching indo stock price for utc timestamp {}".format(self.dt_1))
        pipeline = "[{'$match':{updatedAt: {'$gte':ISODate('%s'),'$lt':ISODate('%s')}}},{'$sort':{updatedAt:-1}},{'$group':{_id:'$stockCode',closePrice:{'$first':'$closePrice'}}}]" % (self.dt_2, self.dt_1)
        current_day_price = self.io_utils.read_from_mongodb(
                                            self.config["data_store"]["pricing_mongo"],
                                            self.config["data_store"]["current_indo_stock_price"]["collection"],
                                            pipeline)
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["indo_stock"]["price_path"])
        self.dedupe_price_and_write_to_s3(current_day_price, s3_path, ["_id"], 1, "indo_stock_price")
        self.logger.info("fetched indo stock price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_crypto_currency_price(self):
        self.logger.info("fetching crypto currency price for utc timestamp {}".format(self.dt_1))
        pipeline = "[{'$match':{startTime: {'$gte':ISODate('%s'),'$lt':ISODate('%s')}}},{'$sort':{startTime:-1}},{'$group':{_id:'$cryptoCurrencyId',mid_price:{'$first':'$closeMidPrice'},buy_price:{'$first':'$closeBuyBackPrice'},sell_price:{'$first':'$closeSellPrice'}}}]" % (self.dt_2, self.dt_1)
        current_day_price = self.io_utils.read_from_mongodb(
                                                self.config["data_store"]["pricing_mongo"],
                                                self.config["data_store"]["crypto_currency"]["collection"],
                                                pipeline)
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["crypto_currency"]["price_path"])
        self.dedupe_price_and_write_to_s3(current_day_price, s3_path, ["_id"], 1, "crypto_currency_price")
        self.logger.info("fetched crypto currency price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_crypto_currency_futures_price(self):
        self.logger.info("fetching crypto currency futures price for utc timestamp {}".format(self.dt_1))
        pipeline = "[{'$match':{startTime: {'$gte':ISODate('%s'),'$lt':ISODate('%s')}}},{'$sort':{startTime:-1}},{'$group':{_id:'$cryptoFuturesId',close_price:{'$first':'$currentPrice'},high_price:{'$first':'$highPrice'},low_price:{'$first':'$lowPrice'},open_price:{'$first':'$openPrice'}}}]" % (self.dt_2, self.dt_1)
        current_day_price = self.io_utils.read_from_mongodb(
                                                self.config["data_store"]["crypto_futures_pricing_mongo"],
                                                self.config["data_store"]["crypto_currency_futures"]["collection"],
                                                pipeline)
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["crypto_currency_futures"]["price_path"])
        self.dedupe_price_and_write_to_s3(current_day_price, s3_path, ["_id"], 1, "crypto_currency_futures_price")
        self.logger.info("fetched crypto currency futures price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_gold_price(self):
        self.logger.info("fetching gold price for utc timestamp {}".format(self.dt_1))
        pipeline = "[{'$match':{createdDate: {'$gte':ISODate('%s'),'$lte':ISODate('%s')}}},{'$sort':{createdDate:-1}}]" % (self.dt_2, self.dt_1)
        current_day_price = self.io_utils.read_from_mongodb(
                                            self.config["data_store"]["reporting_mongo"],
                                            self.config["data_store"]["gold"]["collection"],
                                            pipeline)
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["gold"]["price_path"])
        self.dedupe_price_and_write_to_s3(current_day_price, s3_path, ["partnerId"], 1, "gold_price", s3_file_type="json")
        self.logger.info("fetched gold price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_forex_price(self):
        self.logger.info("fetching forex price for utc timestamp {}".format(self.dt_1))
        forex_partner_prices = self.io_utils.read_from_postgres(self.config['data_store']['forex_postgres'],
                                                            self.config['data_store']["forex"]["db_table"])
        forex_partner_prices = forex_partner_prices.where(col("updated") <= self.dt_1).orderBy(col("updated").desc())
        w2 = Window.partitionBy("forex_id", "partner_id").orderBy(col("updated").desc())
        forex_partner_prices = forex_partner_prices.withColumn("row", row_number().over(w2)).filter(col("row") == 1).drop("row")
    
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["forex"]["price_path"])
        if (forex_partner_prices.count()) < 1:
            forex_partner_prices = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(s3_path, self.t_2, self.h_2))
        self.io_utils.write_json_file(forex_partner_prices, "{}/dt={}/hour={}/".format(s3_path, self.t_1, self.h_1))
        self.logger.info("fetched forex price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_fund_price(self):
        self.logger.info("fetching fund price for utc timestamp {}".format(self.dt_1))
        fund_partner_prices = self.io_utils.read_from_postgres(self.config['data_store']['fund_postgres'],
                                                           self.config['data_store']["fund"]["db_table"])
    
        fund_partner_prices = fund_partner_prices.where((col("partner_id") == self.config["fund"]["partner_id"]) & (
                date_format(from_utc_timestamp(col("updated"), "Asia/Jakarta"), "yyyy-MM-dd") <= self.t_1)).orderBy(col("updated").desc())
    
        w2 = Window.partitionBy("fund_id").orderBy(col("updated").desc())
        current_fund_prices = fund_partner_prices.withColumn("row", row_number().over(w2)).filter(col("row") == 1).drop("row")
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["fund"]["price_path"])

        self.io_utils.write_json_file(current_fund_prices, "{}/dt={}/hour={}/".format(s3_path, self.t_1, self.h_1))
        self.logger.info("fetched fund price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def get_global_stock_price(self):
        self.logger.info("fetching global stock price for utc timestamp {}".format(self.dt_1))
        pipeline = "[{'$match':{endTime: {'$gte':ISODate('%s'),'$lte':ISODate('%s')}}},{'$sort':{endTime:-1}},{'$group':{_id:'$globalStockId',mid_price:{'$first':'$midClosePrice'},buy_price:{'$first':'$buyBackClosePrice'},sell_price:{'$first':'$sellClosePrice'}}}]" % (self.dt_2, self.dt_1)
        current_day_price = self.io_utils.read_from_mongodb(
                                                self.config["data_store"]["pricing_mongo"],
                                                self.config["data_store"]["global_stock"]["collection"],
                                                pipeline)
        if current_day_price.count() > 0:
            # Convert MongoDB ObjectId struct to string for joining
            current_day_price = current_day_price.withColumn("_id", col("_id.oid").cast("string"))
            current_day_price = current_day_price.withColumn("priority", lit(2))
            self.logger.info(f"printing current_day_price")
            current_day_price.show(10, truncate=False)
            current_day_price.printSchema()
            global_stocks = self.get_global_stocks()
            self.logger.info(f"printing global_stocks")
            global_stocks.show(10, truncate=False)
            global_stocks.printSchema()
            linked_stock_price = current_day_price.join(global_stocks, on=["_id"], how="left") \
                .filter(col("linked_stock_id").isNotNull()) \
                .drop("_id") \
                .withColumnRenamed("linked_stock_id", "_id") \
                .withColumn("_id", col("_id").cast("long")) \
                .withColumn("priority", lit(1))
            current_day_price = current_day_price.unionByName(linked_stock_price)
            current_day_price = self.ops.de_dupe_dataframe(current_day_price, ["_id"], "priority").drop("priority")
        else:
            self.logger.info("Price not found for any global stock for the startTime {} and endTime {}.".format(self.dt_2, self.dt_1))
        s3_path = "{}/{}".format(self.bucket_path, self.config["prices"]["global_stock"]["price_path"])
        self.dedupe_price_and_write_to_s3(current_day_price, s3_path, ["_id"], 1, "global_stock_price")
        self.logger.info("fetched global stock price successfully for utc timestamp {}".format(self.dt_1))
    
    
    def start_processing(self):
        self.get_global_stock_price()
        self.get_gold_price()
        self.get_fund_price()
        self.get_forex_price()
        self.get_crypto_currency_price()
        self.get_crypto_currency_futures_price()
        self.get_global_stock_options_price()

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
